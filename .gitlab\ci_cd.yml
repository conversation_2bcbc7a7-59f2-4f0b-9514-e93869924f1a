include:
  - remote: 'https://git.cvc.com.br/devops/gitlab/ci-cd-template-files/raw/release/2.0.0/default_jobs.yml'

variables:
  BUILD_IMAGE: node:16.20-slim
  BUILD_CMD: |
    apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*
    npm cache clean --force
    npm install --legacy-peer-deps
    npm run tsc:version
    npm run build
  DOCKERFILE_PATH: Dockerfile
  TECHNOLOGY: nodejs

stages:
  - local-merge
  - build
  - scan
  - configure-infra
  - push-argocd-repo
