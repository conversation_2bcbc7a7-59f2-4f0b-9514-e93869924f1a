include:
  - remote: 'https://git.cvc.com.br/devops/gitlab/ci-cd-template-files/raw/release/2.0.0/default_jobs.yml'

variables:
  BUILD_IMAGE: node:14.15-alpine3.13
  BUILD_CMD: |
    npm install && npm run build
  DOCKERFILE_PATH: Dockerfile
  TECHNOLOGY: nodejs

default:
  image: $BUILD_IMAGE
  before_script:
    - ln -sf /bin/bash /bin/sh

stages:
  - local-merge
  - build
  - scan
  - configure-infra
  - push-argocd-repo
