# Etapa de build da aplicação Node.js
FROM node:14.15-alpine3.13 AS build
WORKDIR /opt/oracle

RUN apk update && apk add --no-cache libaio unzip wget zip curl bash

RUN wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip && \
    unzip instantclient-basiclite-linuxx64.zip && \
    rm -f instantclient-basiclite-linuxx64.zip && \
    cd instantclient* && \
    rm -f *jdbc* *occi* *mysql* *jar uidrvci genezi adrci && \
    echo /opt/oracle/instantclient* > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

WORKDIR /corp-wl-vhs-bff
COPY . .
RUN npm install && npm run build
RUN zip -r corp-wl-vhs-bff.zip . -x /node_modules/*

# *** Etapa do Veracode removida ***

# Etapa final para criar a imagem de execução
FROM node:14.15-alpine3.13
WORKDIR /opt/oracle

RUN apk update && apk add --no-cache libaio curl bash

# Reutilizar a configuração do Oracle Instant Client
COPY --from=build /opt/oracle /opt/oracle

RUN echo /opt/oracle/instantclient* > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

WORKDIR /corp-wl-vhs-bff
COPY --from=build /corp-wl-vhs-bff /corp-wl-vhs-bff

ENV PORT 8080

EXPOSE 8080

ENTRYPOINT ["npm", "start"]
