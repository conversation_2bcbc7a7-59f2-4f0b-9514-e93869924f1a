# Etapa de build da aplicação Node.js
FROM node:16.20-slim AS build
WORKDIR /opt/oracle

RUN apt-get update && apt-get install -y libaio1 unzip wget zip curl && rm -rf /var/lib/apt/lists/*

RUN wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip && \
    unzip instantclient-basiclite-linuxx64.zip && \
    rm -f instantclient-basiclite-linuxx64.zip && \
    cd instantclient* && \
    rm -f *jdbc* *occi* *mysql* *jar uidrvci genezi adrci && \
    echo /opt/oracle/instantclient* > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

WORKDIR /corp-wl-vhs-bff
COPY . .
RUN npm install && npm run build
RUN zip -r corp-wl-vhs-bff.zip . -x /node_modules/*

# *** Etapa do Veracode removida ***

# Etapa final para criar a imagem de execução
FROM node:16.20-slim
WORKDIR /opt/oracle

RUN apt-get update && apt-get install -y libaio1 curl && rm -rf /var/lib/apt/lists/*

# Reutilizar a configuração do Oracle Instant Client
COPY --from=build /opt/oracle /opt/oracle

RUN echo /opt/oracle/instantclient* > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

WORKDIR /corp-wl-vhs-bff
COPY --from=build /corp-wl-vhs-bff /corp-wl-vhs-bff

ENV PORT 8080

EXPOSE 8080

ENTRYPOINT ["npm", "start"]
