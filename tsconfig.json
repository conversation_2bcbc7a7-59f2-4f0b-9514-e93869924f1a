{"compilerOptions": {"target": "es2018", "module": "commonjs", "lib": ["ES2018"], "allowJs": true, "sourceMap": true, "outDir": "./dist", "strict": false, "noImplicitAny": false, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true}, "compileOnSave": false, "include": ["./src/**/*"], "exclude": ["node_modules", "dist"]}